<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>simple-atm</title>                                                                                                                                                   
       <link rel="stylesheet" type="text/css" href="style.css">             
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col display">
              <div class="cal-logo">Atm</div>
              <div class="massege"><p id="msg2"></p>
                <p id="msg">Enter your Atm No.</p></div>
                <div class="menu" id="menu">
            <input type="button" value="withdraw" onclick="withdraw()" >
            <input type="button" value="Credit" onclick="credit()" >
            <input type="button" value="Mini-st." onclick="minist()" >
            <input type="button" value="Exit" onclick="exit()">


    </div>
          <div id="mini-st">
              <p id="msg3">
                  <span id="Ac-holder"> <b>Account-holder&nbsp;&nbsp;</b><PERSON></span> <br>
                  <span id="main-balance">5000</span> <br>
                  <span id="credit">credit</span> <br>
                  <span id="debit">debit</span>
              </p>
          </div>
          <input type="button" value="&#8635;" onclick="captcha()" id="captcha">
           <input type="button" value="Back" onclick="menuback()" id="menuback">
            </div>
            <div class="col calculator">
                <form class="cal-body" name="forms" id="forms">

        <input type="text" class="cal-display" name="answer" id="textview" disabled>
        <div class="cal-button">

            <input  type="button" value="1"  onclick="forms.answer.value += '1'"           id="btn1" class="btn">
            <input  type="button" value="2"  onclick="forms.answer.value += '2'"           id="btn2" class="btn">
            <input  type="button" value="3"  onclick="forms.answer.value += '3'"           id="btn3" class="btn">
            <input  type="button" value="4"  onclick="forms.answer.value += '4'"           id="btn4" class="btn">
            <input  type="button" value="5"  onclick="forms.answer.value += '5'"           id="btn5" class="btn">
            <input  type="button" value="6"  onclick="forms.answer.value += '6'"           id="btn6" class="btn">
            <input  type="button" value="7"  onclick="forms.answer.value += '7'"           id="btn7" class="btn">
            <input  type="button" value="8"  onclick="forms.answer.value += '8'"           id="btn8" class="btn">
            <input  type="button" value="9"  onclick="forms.answer.value += '9'"           id="btn9" class="btn">
            <input  type="button" value="0"  onclick="forms.answer.value += '0'"           id="btn0" class="btn">
            <input  type="button" value="."  onclick="forms.answer.value += '.'"           id="btn." class="btn">

            <input   type="button" value="C"  onclick="back()"           id="btncancel" class="btn cancel">
            <input   value="Reset"  onclick="forms.answer.value=delete(forms.answer.value)"          id="btnreset" type="reset" class="btn reset">
           <input type="button"  value="Procced"  onclick="cardcheck()"          id="btnproced" class="btn equal">
        </div>
    </form>
            </div>
        </div>
    </div>
    <script>
             var toammount=5000;
            var tammount=parseInt(toammount);
        function back(){
            var exp=document.getElementById('forms').textview.value

            exp = exp.substring(0,exp.length - 1);

            document.getElementById('forms').textview.value=exp;  
        };


    function cardcheck() {

        document.getElementById('msg').innerHTML="Processing...";
        var textv=document.getElementById('textview').value;
       if(textv=="")
           {
               document.getElementById('msg').innerHTML="Card is no entered";
           }
        else{
             if(textv==1111)
                 {
                      document.getElementById('msg').style.color="white";
                      document.getElementById('msg2').innerHTML="Card No. is correct";
                      document.getElementById('msg').innerHTML="Enter your password";
                     document.getElementById('textview').value="";
                     document.getElementById('btnproced').setAttribute("onclick","passcheck()");
                 }
            else{
                document.getElementById('msg').innerHTML="Card No. is incorrect";
                document.getElementById('msg').style.color="red";
            }
        }
        };

        function passcheck() {
            var textv=document.getElementById('textview').value;
          if(textv=="")
           {
               document.getElementById('msg').innerHTML="password is no entered";

           }
        else{
             if(textv==1234)
                 {
                      document.getElementById('msg').style.color="white";
                      document.getElementById('msg2').innerHTML="password is correct";
                      document.getElementById('msg').innerHTML="choose option";
                     document.getElementById('textview').value="";
                      document.getElementById('menu').style.display="block";
                     document.getElementById('textview').style.display="none";
                      document.getElementById('btnproced').setAttribute("onclick","");

                 }
            else{
                document.getElementById('msg2').innerHTML="";
                document.getElementById('msg').innerHTML="password is incorrect";
                                document.getElementById('msg').style.color="red";

            }
        }
        };
        function withdraw() {

            document.getElementById('msg2').innerHTML="this is your captcha code, please Enter";
              document.getElementById('menu').style.display="none";
            document.getElementById('menuback').style.display="block";

             document.getElementById('btnproced').setAttribute("onclick","captchavarification()");
            document.getElementById('textview').style.display="block";
            captcha();



        };
        function calculation1() {

             var textview=document.getElementById('textview').value;
            var textv=parseInt(textview);
            if(textv=="" || textv==0)
                {
                    document.getElementById('msg2').innerHTML="ammount is not entered";
                    document.getElementById('msg').innerHTML="";
                }
            else{
                if(tammount>1 && textv<tammount){
                     tammount=tammount-textv;
             document.getElementById('msg2').innerHTML="your balance";
            document.getElementById('msg').innerHTML=tammount;
             document.getElementById('textview').value="";
                }
                else{
                    document.getElementById('msg').innerHTML="insufficient balance!!!!!!!!";
                }

            }

        };
        function credit() {

            document.getElementById('msg').innerHTML="Enter your Ammount";
              document.getElementById('menu').style.display="none";
            document.getElementById('menuback').style.display="block";
             document.getElementById('msg2').innerHTML="";
             document.getElementById('btnproced').setAttribute("onclick","calculation2()");
            document.getElementById('textview').style.display="block";


        };
        function calculation2() {
             var textv=document.getElementById('textview').value;
            if(textv=="" || textv==0)
                {
                    document.getElementById('msg2').innerHTML="ammount is not entered";
                    document.getElementById('msg').innerHTML="";
                }
            else{
                tammount=tammount+parseFloat(textv);
             document.getElementById('msg2').innerHTML="your balance";
            document.getElementById('msg').innerHTML=tammount;
             document.getElementById('textview').value="";
            }


        };
        function menuback() {
            document.getElementById('menu').style.display="block";
            document.getElementById('menuback').style.display="none";
             document.getElementById('btnproced').setAttribute("onclick","");
            document.getElementById('mini-st').style.display="none";
            document.getElementById('msg').innerHTML="choose option";
            document.getElementById('msg2').innerHTML="";
                        document.getElementById('captcha').style.display="none";
        };
        function minist() {
             var textv=document.getElementById('textview').value;
             document.getElementById('mini-st').style.display="block";
            document.getElementById('textview').style.display="none";
            document.getElementById('msg').innerHTML="Your Mini Statement is:-";
            document.getElementById('msg2').innerHTML="";
             document.getElementById('main-balance').innerHTML=tammount;
             document.getElementById('menuback').style.display="block";
             document.getElementById('credit').innerHTML=textv;
             document.getElementById('debit').innerHTML=textv;
            document.getElementById('menu').style.display="none";


        };

//        captcha code

         function captcha(){

        var captcha1=Math.random()*9999;
    captcha1=Math.floor(captcha1);       
        var ra=document.getElementById('msg').innerHTML=captcha1;
             document.getElementById('captcha').style.display="block";

    };
function captchavarification() {
    var varifycaptcha=document.getElementById('msg').textContent;
document.getElementById('msg2').innerHTML="";
    var textview=document.getElementById('textview').value;
    if(varifycaptcha==textview)
        {
            document.getElementById('msg2').innerHTML="captcha matched successfully!!!!!!!!!!"
             document.getElementById('btnproced').setAttribute("onclick","calculation1()");
            document.getElementById('textview').value="";
            document.getElementById('msg').innerHTML="Enter your ammount";
            document.getElementById('captcha').style.display="none";


        }
    else{
        document.getElementById('msg2').innerHTML="captcha not match. Try again";
         document.getElementById('btnproced').setAttribute("onclick","captchavarification()");
         document.getElementById('textview').value="";
        document.getElementById('msg1').innerHTML="Enter your captcha again";
    }

};
function exit(){
    document.getElementById('msg').innerHTML="Enter your Atm No.";
    document.getElementById('btnproced').setAttribute("onclick","cardcheck()");
     document.getElementById('msg2').innerHTML="";
    document.getElementById('menu').style.display="none";
    document.getElementById('textview').value="";
     document.getElementById('textview').style.display="block";
}
</script>
</body>
</html>