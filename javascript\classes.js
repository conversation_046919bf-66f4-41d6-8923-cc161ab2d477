class BankAccount{
customerName;
accountNumber;
balance=0;

constructor(customerName,balance=0){
this.customerName=customerName;
this.accountNumber =Date.now();
this.balance=balance;
}


deposit(amount){
this.balance+=amount;
}

withdraw(amount){
this.balance-=amount;
}

}

const kalyaniAccount=new BankAccount("kalyani saini",10000);
kalyaniAccount.deposit(200);

const avnniAccount=new BankAccount("Avni");
avnniAccount.deposit(5000);
avnniAccount.withdraw(1000);

console.log(kalyaniAccount);
console.log(avnniAccount);

//hoisting.... 
/*hello();
function hello(){
    console.log('Hello');
}*/

