 //Asynchronous javascript
 //code runs without blocking either if another code starts running
 //used in event listner
 //=> call stack => web Apis => Callback Queue=>
    //if call stack is not empty then element will wait in callback queue for the empty of the call stack
 
  /*console.log('Hello')

  function queue(){
console.log('I am from setTimeout..')
  }
 setTimeout(queue,3000); //3second//loupe
console.log('Javascript')*/

console.log('Start')
setTimeout(()=>{
    console.log('I am from Settimeout')
},0)
//setTimeout is asyncronous that means it will run after all weather time is 0
console.log('End')

