function BankAccount(customerName,balance=0){
    this.customerName=customerName;
    this.accountNumber=Date.now();//gives always diff no.
    this.balance=balance;

this.deposit=function(amount){
this.balance+=amount;
};
this.withdraw=function(amount){
this.balance -=amount;
};
}

const kalyaniAccount=new BankAccount("kalyani S");
// const AvniAccount=new BankAccount("Avni S");

// console.log(kalyaniAccount);
// console.log(AvniAccount);

//BankAccount.prototype.test='this is a test';
 BankAccount.prototype.deposit=function(amount){
    this.balance+=amount;
};

BankAccount.prototype.withdraw=function(amount){
    this.balance-=amount;
};

console.log(kalyaniAccount);
kalyaniAccount.deposit(1000);
// console.log(BankAccount.prototype);


