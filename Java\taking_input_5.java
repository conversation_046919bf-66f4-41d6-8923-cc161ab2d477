import java.util.Scanner;
public class taking_input_5
 {
    public static void main(String[] args) {
        System.out.println("Taking input from the user");
        // boolean b1 = sc.hasNextInt();
        // System.out.println(b1);
        Scanner sc = new Scanner(System.in);
        System.out.println("Enter no1");/
        int a = sc.nextInt();
        System.out.println("Enter no.2");
        int b = sc.nextInt();
        System.out.println("Enter no3");
        int c = sc.nextInt();
        System.out.println("Enter no.4");
        int d = sc.nextInt();
        System.out.println("Enter no5");
        int e = sc.nextInt();
       int sum = a+b+c+d+e;
        System.out.println("The sum of these  number is:");
        System.out.println(sum);
       float  perc = sum/5;
       System.out.println("Percentage of student is:");
       System.out.println(perc);

       
        // String str = sc.next();
        // System.out.println(str);

    }
    
}
