   //console.log('Hello ');
   //alert('Hiiii Baby');
  
  //Objects
  /* const laptop = {
   model: 'xxx',
   color: 'white',
  }
//for sinle line commenting
/* for multiple line commenting
  console.log(laptop);
   console.log(laptop.color);
*/

   //variables
   /*/declaired by var,let,const
   /let language= 'Javascript';
   console.log(language);*/
   
   /*const secondsInminute = 60;
   secondsInminute = 30;*/
   //always constant not change at all

   //function scope
  // var language ='Javascript';
   
 /* //block scope
if(true){
   let age= 25;
   console.log(age);
}*/

//hoisting= means code run line by line 

/*console.log(greeting);
var greeting = 'hello';*/
//undefined is datatype in js for var by default

/*console.log(greeting);
let greeting = 'hello';
//hoisting works in const and let also but in some zone that zone is called temporal dead zone
*/
//const is used on prior basis
 //for changing requirement use let

  