<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML Forms</title>
</head>
<body>
    <h1>HTML FORM</h1>
    <form action="thanku.html" method="get" ><!--used for storing data in backend-->
        <label for="username">Username : </label>
        <input type="text" id="username" placeholder="username" required>
        <br><br>
        <label for="password">Password : </label>
        <input type="password" id="password" name="password" placeholder="password" required>
        <br><br>
        <label for="email">Email : </label>
        <input type="email" id="email" name="email" placeholder="email" required>
        <br><br>
        <!--radio button-->
        <p>Select your gender</p>
        <label for="male">Male</label>
        <input type="radio" name="gender" id="male" value="male"><br>
        <label for="female">Female</label>
        <input type="radio" name="gender" id="female" value="female"><br>
        <label for="other">Other</label>
        <input type="radio" name="gender" id="other" value="other">
        <br><br>
        <!-- to select only one attribute in radio we use same name for all -->
         <label for="country">Country</label>
         <select id="country">
         <option value="India">India</option>
         <option value="Bhutan">Bhutan</option>
         <option value="America">America</option>
         <option value="Nepal">Nepal</option>
        </select>
         <br><br>
        <label for="age-group">Select your age group</label>
        <select name="age-group values" id="age-group">
            <option value="5-10">5-10</option>
            <option value="11-21">11-21</option>
            <option value="21-50">21-50</option>
            <option value="50+">50+</option>
        </select><br>
        <p>comment below</p>
        <textarea name="" id="" cols="30" rows="10" placeholder="type here"></textarea>
        <br>
        <label for="">Pick Color : </label>
        <input type="color" name="" id=""><br><br>
        Date<input type="date"><br><br>
        <input type="file">

        <br><br>
        <input type="submit" value="Submit">

    </form>
</body>
</html>