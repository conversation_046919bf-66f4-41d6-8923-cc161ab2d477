//DOM manipulaton Document Object Model
//1.getElementById
//const heading = document.getElementById('heading');
//console.log(heading);

//2.getElementByTagName
//const heading = document.getElementsByTagName('h1');
//console.log(heading[0]);

//3.getElementByClassName
// const heading = document.getElementsByClassName('heading');
// console.log(heading);

//4.queryselector
// const heading=document.querySelector('.heading');
// console.log(heading);

//5.querySelectorall
//const heading= document.querySelectorAll('.heading');
//console.log(heading);

//traverse DOM 
 //1.parentNode
// const heading = document.querySelector('.heading');
// const parent = heading.parentNode;
// console.log(parent);

//2.child Nodes
//const parent = document.querySelector('.parent');
//console.log(parent.childNodes);

//3.nextElementSibling
// const heading = document.querySelector('.heading');
// console.log(heading.nextElementSibling);

//4.previousElementSibling
// const subHeading = document.querySelector('h3');
// console.log(subHeading.previousElementSibling);

//Manipulation
/*const heading=document.querySelector('.heading');
heading.innerHTML='web dev is awesome!'; 
heading.style.color ='red';
heading.style.fontSize='100px';
heading.classList.add('title');
 heading.classList.remove('heading');*/

 //create elements
/*const heading=document.createElement('h1');
heading.innerHTML='javascript is massttt';
heading.classList.add('title');
const parent= document.querySelector('.parent')
parent.appendChild(heading);

const subHeading=document.createElement('h3');
subHeading.innerHTML='you are great';
heading.insertAdjacentElement('afterend',subHeading);

console.log(heading);*/

//DOM events
/*const button= document.querySelector('#btn');
const heading= document.querySelector('#heading');

button.addEventListener('click',function(event){
    heading.style.color = 'purple';
    heading.style.fontSize = '60px';
    console.log('button clicked',event);
})*/




