/*const languages = ['java' , 'python' , 'C++'];
//Length 
//console.log(languages.length);
//indexing = strt from 0 index
languages.push('dart');//to add in the last of array
languages.unshift('javaScript');
languages.pop();//to delete last element
languages.shift();//to delete first element
console.log(languages);*/

//loop 
/*for(let i =0; i<4;i++){
console.log('*');
 }*/

/*const actors = [
    {  
        name: 'Actor 1',
        payment: 100
    },
    {  
        name: 'Actor 2 ',
        payment: 200
    },
    {  
        name: 'Actor 3',
        payment: 150
    }
];*/
/*for(let i=0; i<actors.length;i++){
    actors[i].payment = actors[i].payment -10;
}
*/
//for each
/*actors.forEach((actor)=>{
    actor.payment = actor.payment - 10;
console.log(actor); 
})*/
//console.log(actors);
/*for(let actor of actors){
    actor.payment = actor.payment - 10;
    console.log(actor);
}*/

//filter 
/*const students=[
    {
    name:'Student 1',
    marks: 45
    },
    {
    name:'Student 2',
    marks: 60
    },
    {
    name:'Student 3',
    marks: 35,
    }
]
const failed = students.filter((student)=>{
if(student.marks<45){
 //   return true;
//}
  //  return false;

return student.marks<45;
  })
console.log(failed);*/

//Map
/*const users = [
    {
        fname:'John',
        lname:'Doe'
    },
    {
        fname:'Ram',
        lname:'Doe'
    },
    {
        fname:'Om',
        lname:'Doe'
    }
];
const finalUsers= users.map((user)=>{
return {
    fullname:`${user.fname} ${user.lname}` 
};
});
console.log(finalUsers);*/

//Reduce
/*const movies =[
{
    name:'Intersteller',
    budget:100
},
{
    name:'Social',
    budget:150
},
{
    name:'Matrix',
    budget:300
},
];
//let total=0;
//movies.forEach((movie)=>{
 //   total=total+movie.budget;
//});
const total = movies.reduce((acc,movie)=>{
acc=acc+movie.budget;
return acc;
},0)
console.log(total);*/

//indexof
/*const admins=[2,1,5];

const user={
    name:'XYZ',
    id:5
}
const isAdmin=admins.*/

//includes
/*const admins=[2,1,5];

const user={
    name:'XYZ',
    id:5
}
console.log(admins.includes(user.id));*/

//find

/*const users=[
    {
        name:'XYZ',
        id:1
    },
        {
        name:'abc',
        id:2
    },
        {
        name:'pqr',
        id:3
    },
];
const myUser=users.find((user)=>{
if(user.id === 2){
    return true;
}
return false; 
});
console.log(myUser)*/

//sort
/*const names=['John','Jane','Shyam','Ram','Sameer','Mansi'];
names.sort();
console.log(names);*/

//Splice
/*const names=['John','Jane','Shyam','Ram','Sameer','Mansi'];
names.splice(2,2);
console.log(names)*/

