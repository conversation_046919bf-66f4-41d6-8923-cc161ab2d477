<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title> HTML id and classe attribute </title>
    <style>
        .heading1{
            color: red;
        }
        #lines{
            color: brown;
        }
    </style>
</head>
<body>
    <h2 class="heading1">This is Heading</h2>
    <p id="lines">Lorem ipsum dolor sit amet consectetur adipisicing elit. Minus ea earum quaerat quae soluta dolore laudantium magnam debitis, similique, aspernatur deserunt voluptatum molestiae quas reiciendis nemo asperiores atque rerum, eum quo eaque incidunt. Impedit totam voluptatibus at quis dolor consectetur. Distinctio fugit magnam cum ut, neque dolores dolore esse eligendi.</p>
    <!-- class can be used many times and helps in styling only whereas id can be used only once at a time and can also be used in js ,react -->
    <h2>this is also Heading</h2>
    <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Labore odio aut voluptas, recusandae impedit repellendus quia autem sunt nesciunt aspernatur ducimus pariatur animi repellat et, quisquam optio eveniet neque odit architecto omnis. Et qui voluptates nihil nulla voluptate vero expedita inventore fuga a dicta voluptatum, architecto suscipit quis voluptatem? Quasi.</p>
</body>
</html>