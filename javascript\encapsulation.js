class BankAccount{
customerName;
accountNumber;
#balance=0;

constructor(customerName,balance=0){
this.customerName=customerName;
this.accountNumber =Date.now();
this.#balance=balance;
}

deposit(amount){
this.#balance+=amount;
}

withdraw(amount){
this.#balance-=amount;
}

set balance(amount){
if(isNaN(amount)){
    throw new Error('Amount is not a valid number');
}

this.#balance=amount;
}

get balance(){
    returnthis.#balance;
}

}

class CurrentAccount extends BankAccount{
    transactionLimit=50000;

    constructor(customerName,balance=0){
        super(customerName,balance);
       
    }
   

    calculateInterest(){
        console.log('Calculating interest');
    }
     takeBusinessLoan(amount){
        //logic
        this.calculateInterest(amount);
     console.log('Taking business loan:' + amount);
  }
}

const kalyaniAccount=new BankAccount('kalyani s',2000);
// kalyaniAccount.setbalance=(400);
// kalyaniAccount.balance=5000;
kalyaniAccount.takeBusinessLoan(100000);
console.log(kalyaniAccount);




