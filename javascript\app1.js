/*let age=21;
age= 'TwentyOne';
console.log(age);*/


//Primitive data types
//1.Number
/*let age=21;
let temp=-21;
let price=20.009;*/

//2.String
/*let name = '<PERSON><PERSON><PERSON>';*/

//3.boolean   =true/false
/*let isloggedIn =true;
let hasPermission=false;*/

//4.undefined = defined value is not assigned 
/*let age;
console.log(age);*/

//5.BigInt

//6.Symbol  =Unique


//Structural Type
//1.Object =hash maping having key and symbol
  //1.1 Function= non-data structure which is callable or can be reused
  //1.2 Arrays  =collection of values
  const number = [2,3,4,'Javascript',{name: '<PERSON><PERSON><PERSON>'}];
  //1.3 Maps
  //1.4 Set = stores only unique value ,no duplication
  //1.5 Date


//Structural root
 //1. Null  = empty value




