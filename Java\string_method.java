public class string_method {
    public static void main(String[] args) {
        String name="Kalyani";
        //System.out.println(name);
        //int value = name.length();
        //System.out.println(value);
        //String lstring = name.toLowerCase();
        //System.out.println(lstring);
       // String ustring = name.toUpperCase();
        //System.out.println(ustring);
        //String nonTrimmedString = "     Kalyani   ";
        //System.out.println(nonTrimmedString);
        //System.out.println(nonTrimmedString.trim());
          // System.out.println(name.substring(2));
           //System.out.println(name.substring(2, 7));
           //System.out.println(name.replace('l','t'));
           //System.out.println(name.replace("aly","inya"));
          // System.out.println(name.startsWith("Ka"));
     System.out.println(name.charAt(1  ));
     //String modifiedName ="Harryrry";
     //System.out.println(modifiedName.indexOf("rry"));
     //System.out.println(modifiedName.indexOf("rry",4));
     //System.out.println(modifiedName.lastIndexOf("rry"));
     System.out.println(name.equals("Kalyani"));
     System.out.println(name.equalsIgnoreCase("kalyani"));
    }
}
