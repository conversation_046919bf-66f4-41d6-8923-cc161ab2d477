// project on bank BankAccount
function BankAccount(customerName,balance=0){
    this.customerName=customerName;
    this.accountNumber=Date.now();//gives always diff no.
    this.balance=balance;

    //methods
this.deposit=function(amount){
this.balance+=amount;
};
this.withdraw=function(amount){
this.balance -=amount;
};

}

/*const kalyaniAccount=new BankAccount('kalyani ',1000000);
const AvniAccount=new BankAccount('Avni');

kalyaniAccount.deposit(500000);
AvniAccount.deposit(500000);
kalyaniAccount.withdraw(500000);

// kalyaniAccount.balance=200000;//change balance
console.log(kalyaniAccount,AvniAccount);*/

//Dom  manipulation

const accounts=[];
const accountForm=document.querySelector('#accountform');
const customerName=document.querySelector('#customerName');
const balance=document.querySelector('#balance');

const depositeForm=document.querySelector('#depositeForm');
const accountNumber=document.querySelector('#accountNumber');
const amount=document.querySelector('#amount');

accountForm.addEventListener('submit',(e)=>{
    e.preventDefault();//prevent from refresh of page
    const account=new BankAccount(customerName.value,+balance.value);
    console.log(customerName.value,balance.value); 
    accounts.push(account);
    console.log(accounts);
})

depositeForm.addEventListener('submit',(e)=>{
    e.preventDefault();
    const account = accounts.find(
        (account)=> account.accountNumber===  +accountNumber.value
    );
    if(account){
        account.deposit(+amount.value);
        console.log(accounts);
    } else {
        console.log('Account not found!');
    }
})

