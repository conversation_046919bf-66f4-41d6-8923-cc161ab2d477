//ES6 AKA Ecmascript 2015
//let const comes
//var productName='laptop';
//code polute
/*var age=18;
if(age>=18){
    var driving = true;

}
console.log(driving);*/

/*function register(){
var username='kalyani';
var password='12345';
}
console.log(username);*/
 
/*var product='Laptop';
var product="Mobile";
console.log(product);*/

// const product='Laptop';
// product='Mobile';

//console.log(age);
//var age=20;//undefined

/*const age={
    years:19,
};
age.years=20;
console.log(age);*/

//Arrow function
//normal function 
/*function greet(){
    console.log('hello');
}*/

/*const  greet=()=>{
    console.log('Hello');
}
greet();*/

/*const addition =(a,b)=> console.log(a+b);
addition(5,4);*/

/*const addition = a => console.log(a+a);
addition(5);*/

/*const shop={
purchase:()=>{
    console.log(this);
},
};
shop.purchase();*/

/*const myButton=document.querySelector('#myButton');
const shop={
    
    price:100,
    buy:function(){
        //const self =this;
        myButton.addEventListener('click',()=>{
            console.log('I Spend '+ this.price);
        });
    },
};
shop.buy();*/

//Template literals AKA backticks
/*const firstname='Kalyani';
const lastName='Saini';
console.log(firstname+' '+lastName);*/
//more complicated to add +

/*const name="Kalyani";
 const message=`
 Hello ${name}${ 5+6}
 World
 `;
console.log(message);*/

 //Enhanched object literals
 //computed property keys
//Method defination shorthand  
//Property shorthand

/*const keyName='name';
 const product={
    //[keyName]:'Mobile',
    //price: 100,
    buy(){
        console.log('Bought');
    },
 };
product.buy();*/


/*const accessToken='jjdainiv';
const refreshToken='fmfjsbhs';
const user={
//accesToken:accessToken,
accessToken,
//refreshToken:refreshToken,
refreshToken
};
console.log(user);*/

//Destructuring 
/*const user={
    name:'Kalyani',
    age:21,
}
const{name: fullName,age}=user;
 
  console.log(fullName);*/

// const data=['Kalyani',21,'ITian'];
// const[name,age,profession]=data;
// console.log(profession);
// const [count,setCount]=userState;

//Default parametets
/*const register=(name,password,image='test.png')=>{
    const img=image||'test.png';
console.log(name,password,img);
};
register('Kalyani','secret','photo.png');*/

//spread ...
/*const languages=['c','c++','javascript'];
const newlanguages=['typescipt',...languages,'python'];
console.log(newlanguages);*/

/*const settings={
    volume:10,
    brightness:80,
};
const newSettings={
    ...settings,
    volume:20,
};
console.log(newSettings);*/

//rest...
/*const addItems=(...items)=>{
    console.log(items);
}
addItems(3,2,4,10,30);*/

//for of loop
//objects
/*const numbers=[2,3,4,5];
for(const num of numbers{
    console.log(num);
})*/

//string
/*const language='Java';
for(const char of language {
    console.log(char);
})*/

//object
/*const person={
    name:'Kalyani',
    age:21,
    city:'Saharanpur',
}
for(const [key,value] of Object.entries(person)){
console.log(key,value);
}*/

// Promises
/*function login(cb){
        setTimeout(()=>{
console.log('login..');
cb();
        },0);
    }
login(()=>{
    console.log('Redirecting');
});*/

/*function login(){
     return new Promise((resolve,reject)=>{
setTimeout(()=>{ 
        console.log('Login...');
        reject();
    },0);
},);
}
login().then(()=>{
    console.log('Redirecting..');
}).catch(err=>{
    console.log('Error...');
});*/

//find(Array methods)
 /*const users=[{name:'Kalyani'},{name:'Dolly'}];

 const user= users.find((user)=>{
return user.name=='Kalyani';
 });
 console.log(user);*/

//findIndex
/*const users=[{name:'Kalyani'},{name:'Dolly'}];

 const user= users.findIndex((user)=>{
return user.name=='Dolly';
 });
 console.log(user);*/

//set

 //const uniqueNumbers=new Set();
 /*uniqueNumbers.add(3);
 uniqueNumbers.add(5);
 uniqueNumbers.add(3);
 uniqueNumbers.add(6);
 console.log(uniqueNumbers);
 console.log(uniqueNumbers.size);
 console.log(uniqueNumbers.has(6));*/

 /*const numbers=[4,5,6,7,8,4];
 const uniqueNumbers= new Set(numbers);
 console.log(Array.from(uniqueNumbers));*/
   
//map(Hashtables)

//1.order
/*const urls=new Map();
urls.set('google','https://google.com');
urls.set('youtube','https://youtube.com');
console.log(urls.get('google'));
const urlsObj={
    google:'https://google.com',
    youtube:'https://youtube.com',
};
console.log(urlsObj.google);*/

//2.size
/*const urls=new Map();
urls.set('google','https://google.com');
urls.set('youtube','https://youtube.com');
for(const [key,value] of urls){
    console.log(key,value);
}*/


//Classes

/*function Person(name){
    this.name=name;
}
const kalyani=new Person('kalyani');
const saini=new Person('saini');
kalyani.name='avni'
console.log(kalyani);*/

/*class Person{
    name;
    
    constructor(name){
     this.name=name;
     
    }
    greet(){
console.log('Good Morning');
    }
}
class GreatPerson extends Person{
    attitude='cool';
}

//const kalyani=new Person('kalyani');

//const avni=new Person('avni');
const kalyani=new GreatPerson('kalyani');
//avni.name='kalyani';
  console.log(kalyani.greet());*/

  //ES6 Module
 //helper.js
 import { libName,login } from "../javascript/lib.js";
//   console.log(fileName);
import myLogin from './lib.js';
myLogin();
//  console.log(login );


