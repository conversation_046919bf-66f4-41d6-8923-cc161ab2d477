<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OOP</title>
</head>
<body>
    <h2>Coder's Bank</h2>
    <form id="accountform">
        <!-- open account --> 
        <input type="text" placeholder="Customer name" id="customerName">
        <input type="number" placeholder="Initial Balance" id="balance">
        <button>Create Account</button>
     </form>
     <hr>
     <form id="depositeform">
        <!-- deposite balance -->
        <input type="number" placeholder=" accountNumber" id="accountNumber">
        <input type="number" placeholder="amount" id="amount">
        <button>Deposite</button>
     </form>
        <button>Create Account</button>
     </form>
    <script src="../javascript/1_constructor_function.js"></script>
</body>
</html>