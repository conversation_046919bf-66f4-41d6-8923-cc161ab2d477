// class config{
// static dbUser='username';
// static dbPassword='password';
// static apiToken='abcd';
// }

// // const config=new config();
// console.log(config.apiToken);




//increament


let id=1;
class User{
    static cache={
    1:'some value',
}
    static id=1;
    constructor(name,age,income){
        this.name=name;
        this.age=age;
        this.income=income;
        this.id=User.id++;
    }

    static hassInCache(){
        console.log(this.cache);
    }

//     // static compareByAge(user1,user2){
//     //     return user1.age-user2.age;
//     // }
//     // static compareByIncome(user1,user2){
//     //     return user1.income-user2.income;
//     // }
 }

User.hassInCache();
// const user1=new User('Kalyani',21,6500);
// const user2=new User('Avni',20,5000);
// const user3=new User('Dolly',22,6000);

// // const users=[user1,user2,user3];
// // users.sort((a,b)=>a.age-b.age);
// // users.sort(User.compareByIncome);
// console.log(user1,user2,user3);
