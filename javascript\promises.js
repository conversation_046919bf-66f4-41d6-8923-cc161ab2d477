//used to make the data in sequence whether it takes less time or more time
// Callback= function k andr function ko call krna ( higher order function)
// Scenario
/**
 * 1.Register
 * 2.Send welcome email
 * 3.login
 * 4.get user data
 * 5.display user data
 */

function register(){
  return new Promise((resolve,reject)=>{
setTimeout(()=>{
  
  console.log('Register End');
  return resolve('Error while registering');
  },2000);
  });
  
  
}
function sendEmail(){
  return new Promise((resolve,reject)=>{
 setTimeout(()=>{
  return reject('Error while sending Email');  
  console.log('Email End');

},2000);
  })
}
function login(){
  return new Promise((resolve,reject)=>{
setTimeout(()=>{
  console.log('login End');
  resolve();
  },1000);
  }) 
}
function getUserData(){
  return new Promise((resolve,reject)=>{
    setTimeout(()=>{
  console.log('Got User Data');
  resolve();
  },1000);
})
}
function displayUserData(){
 return new Promise((resolve,reject)=>{
   setTimeout(()=>{
     console.log('User Data Display');
     resolve();
  },1000);
})
}
//Callback hell- nesting which makes it complex to prevent code from this we use promises
/*register(function(){
 sendEmail(function(){
    login(function(){
      getUserData(function(){
         displayUserData();
      });
    });  
  });
});*/
/*register().
then(sendEmail).
then(login).
then(getUserData).
then(displayUserData)
.catch((err)=>{
console.log('Error:',err);
});*/

//Async await
async function authenticate(){
  try{
  await register();
  await sendEmail();
  await login();
  await getUserData();
  await displayUserData();
  }catch(err){
    console.log(err);
    throw new Error();
    
  }
}
authenticate().then(()=>{
console.log('All Set!');
}).catch((err)=>{
  console.log(err);
})
//catch is used to catch the error
console.log('other application work');


