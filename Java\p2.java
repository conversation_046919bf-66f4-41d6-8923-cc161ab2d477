import java.util.Scanner;

public class p2 {
    public static void main(String[] args) {
     //1.
        //float a = 7/4.0f * 9/2.0f;
        //.0f is used to make precidence occur otherwise it will result in bodmas maths method 
        //System.out.println(a);
    //2.
   // char grade='B';
    //grade= (char)(grade + 8);
    //System.out.println(grade);
    //decryption
    //grade= (char)(grade - 8);
    //System.out.println(grade);
    //3.
    Scanner sc=new Scanner(System.in);
    int a =sc.nextInt();
    System.out.println(a>8);
    //5.
    System.out.println(7*49/7 + 35/75);

    }
}
