//Assignment Operator
/*let x= 10;
let y;
y=x;
console.log(y);*/

//compound operator
/*let x=10;
let y=20;
x=x+y;// x += y;
console.log(x);*/

//Arithmetic Operator
/*let x=10;
//x=x+1; //x++//x--
console.log(x ** 2);*/

//Logical Operators
//1. Logical AND = &&(both values must be true to result true)
/*let happy = true && false;
console.log(happy);*/

/*const isLoggedIn = true;
const hasPermissions = true;
if( isLoggedIn && hasPermissions){
//Delete an item
console.log('Item has deleted successfully');
}*/

//2.Logical OR || (any one true makes whole true)
/*const happy= true||false;
console.log(happy);*/

//3.logical NOT !(makes opp t to f and f to t)
/*const happy= false;
console.log(!happy);*/
/*const animal ='cat';
console.log(!animal);//for string it will result false*/

//Conditional Operators(Ternary Operator)
//const userRole = 'admin';
/*if(userRole === 'admin'){
console.log('You are an admin');
}else{
console.log('You are not an admin');
}*/
/*
//condition? ' : ';
userRole === 'admin'? console.log('You are an admin'):console.log('You are not an admin');*/

//Comparision Operators
//Equal(==)
//not equal(!=)
//Strict Equal(===) = value and datatype both are same
//Strict not equal(!==)
//Greator than(>)
//Greater than or equal(>=)
//less than (<)
//less than or Equal(<=)

/*console.log(3+10*2); //=  23
console.log((3+10)*2);//=26
console.log(3> 2 && 2>1);//True && True =True*/
