/*const bulbSwitch = document.querySelector('#bulbSwitch');
const bulb = document.querySelector('#bulb');

bulbSwitch.addEventListener('click',function(){
  console.log('Current bulb src:', bulb.src);

  // Check if bulb is currently off (contains 'bulboff')
  if(bulb.src.includes('pic_bulboff.gif')) {
    bulb.src = '../Images/pic_bulbon.gif';
    bulbSwitch.innerHTML = 'TURN OFF';
  } else {
    bulb.src = '../Images/pic_bulboff.gif';
    bulbSwitch.innerHTML = 'TURN ON';
  }
})*/

const root=document.querySelector('#root');
const button= document.querySelector('button');

function createItem(item){
  //card
  const card=document.createElement('div');
  card.classList.add('card');

  //Photo
  const photo=document.createElement('img');
  photo.src=item.thumbnailUrl;
  card.appendChild(photo);

  //title
  const title=document.createElement('h4');
  title.innerHTML=item.title;
  card.appendChild(title);

  root.appendChild(card);
}

function displayImages(items){
  items.forEach(function(item) {
    createItem(item);
  });
}

button.addEventListener('click', function(){
  fetch('https://jsonplaceholder.typicode.com/albums/1/photos')
  .then(res=>res.json())
  .then((items)=>{
    displayImages(items);
  })
});







