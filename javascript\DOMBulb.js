/*const bulbSwitch = document.querySelector('#bulbSwitch');
const bulb = document.querySelector('#bulb');

bulbSwitch.addEventListener('click',function(){
  console.log('Current bulb src:', bulb.src);

  // Check if bulb is currently off (contains 'bulboff')
  if(bulb.src.includes('pic_bulboff.gif')) {
    bulb.src = '../Images/pic_bulbon.gif';
    bulbSwitch.innerHTML = 'TURN OFF';
  } else {
    bulb.src = '../Images/pic_bulboff.gif';
    bulbSwitch.innerHTML = 'TURN ON';
  }
})*/

const root=document.querySelector('#root');
const button= document.querySelector('button');



function displayImages(photos){
console.log(photos);
}


button.addEventListener('click', function(){
  fetch('https://jsonplaceholder.typicode.com/albums/1/photos')
  .then(res=>res.json())
  .then((photos)=>{
    displayImages(photos);
  })
});



