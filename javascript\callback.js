//used to make the data in sequence whether it takes less time or more time
// Callback= function k andr function ko call krna ( higher order function)
// Scenario
/**
 * 1.Register
 * 2.Send welcome email
 * 3.login
 * 4.get user data
 * 5.display user data
 */


function register(callback){
  setTimeout(()=>{
  console.log('Regiser End');
  callback();
  },2000);
  
}
function sendEmail(callback){
   setTimeout(()=>{
  console.log('Email End');
  callback();
  },2000);
}
function login(callback){
    setTimeout(()=>{
  console.log('login End');
  callback();
  },3000);
}
function getUserData(callback){
    setTimeout(()=>{
  console.log('Got User Data');
  callback();
  },2000);
}
function displayUserData(){
   setTimeout(()=>{
     console.log('User Data Display');
  },1000);
}
//Callback hell- nesting which makes it complex to prevent code from this we use promises
register(function(){
 sendEmail(function(){
    login(function(){
      getUserData(function(){
         displayUserData();
      });
    });  
  });
});

console.log('other application work');