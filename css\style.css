*{
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}
input{
    cursor: pointer;
}
.container{
    width: 100vw;
    height: 100vh;
    display: grid;
    place-items: center;
    background-color: #292929;
}
.row{
    width: 400px;
    height: 600px;
    margin: auto;
     display: flex;
    justify-content: space-between;
    flex-direction: column;
}
.col{


    height: 300px;

    padding: 20px;
    text-align: center;
    position: relative;
}
.display{
    box-shadow: 0 0 0 35px #a8a6a6,0 0 0 45px #c2b9b9, inset 0 0 10px rgba(0,0,0,1), 0 5px 20px rgba(0,0,0,.5), inset 0 0 15px rgba(0,0,0,.2);
    width: 100%;
}
.calculator{
    width: 80%;
    display: block;
    margin: auto;
}
.cal-logo{
    position: absolute;
    top: 0;
    left: 0;
    font-size: 40px;
    color: #fff;
    text-shadow: 1px 1px 1px black;
    margin: 10px;
}
#textview{
    width: 150px;
    height: 30px;
    display: block;
    margin: auto;
     position: absolute;top: -82px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 20px;
    background: #fff;
    border-top: none;
    border-right: none;
    border-left: none;
   border-bottom: 2px solid black;
    color: #101010;

}
.btn{
    width: 50px;
    height: 50px;
    margin: 5px;

}
.equal{
    width: 70px;
}
.massege{
    position: absolute;
    top: 20%;
    left: 50%;
     transform: translateX(-50%);
}
.massege p{
    font-size: 22px;
    color: #fff;
    text-shadow: 1px 1px 1px black;
    margin: 10px;
}
#msg2{
    color: green;
}
.menu{
    position: absolute;
    bottom: 0;
    left: 20px;
    display: none;
}
.menu input{
    width: 70px;
    height: 30px;
    padding: 3px;
    margin: 5px;

    text-align: center;
    border: 1px solid #050801; 
    background-color: transparent;
    color: #fff;


}
.menu input:hover{
            background-color: #03e9f4;
            color: #050801;
            box-shadow: 0 0 5px #03e9f4,
                0 0 25px #03e9f4,
                0 0 50px #03e9f4,
                0 0 200px #03e9f4;
    outline: none;
    border: none;

        }
#menuback{
    width: 70px;
    height: 30px;
    padding: 3px;
    margin: 5px;
    position: absolute;
    top: 50%;
    right: 5%;
    text-align: center;
    border: 1px solid #050801; 
    background-color: crimson;
    color: #fff;
border-bottom-left-radius: 30px;
     display: none;


}
#menuback:hover{
    font-weight: 400;
            color: #050801;
            box-shadow: 0 0 5px crimson,
                0 0 25px crimson,
                0 0 50px crimson,
                0 0 200px crimson;
    outline: none;
    border: none;
}
#mini-st{
    width: 100%;
    height: 100%;
    background-color: #fff;
    display: none;
}
#msg3{
    position: absolute;
    top: 54%;
    left: 50%;
    transform: translateX(-50%);
}
#captcha{
    width: 31px;
    height: 22px;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    position: absolute;
    top: 130px;
    right: 29%;
   background-color: #3e3e3e;
    outline: none;
    border: none;
    display: none;
    text-align: center;
    line-height: 18px;

}
#captcha:hover{
     box-shadow: 0 0 5px #3e3e3e, 0 0 25px white, 0 0 50px #3e3e3e, 0 0 200px #3e3e3e;
}